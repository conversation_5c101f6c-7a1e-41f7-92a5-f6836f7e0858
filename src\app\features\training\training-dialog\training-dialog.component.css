.dialog-header {
  padding: 30px 30px 0 30px;
}

.dialog-header h2[mat-dialog-title] {
  font-family: <PERSON><PERSON>;
  font-weight: 400;
  font-size: 22px;
  line-height: 28px;
  letter-spacing: 0px;
}

.training-name {
  font-family: <PERSON><PERSON>;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.25px;
  vertical-align: middle;
  margin: 0;
  padding-bottom: 16px;
}

.dialog-content label {
  font-family: <PERSON><PERSON>;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.15px;
  vertical-align: middle;
}

.dialog-content button[mat-button] {
  border: 1px solid #e5e5f4;
}

.dialog-content span {
  font-family: <PERSON><PERSON>;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.1px;
  text-align: center;
  vertical-align: middle;
}

.dialog-content {
  padding: 30px;
  overflow-y: auto;
  flex: 1;
}

.dialog-divider {
  width: 100%;
  height: 1px;
  background-color: #e0e0e0;
  margin: 0;
}

.form-input {
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  line-height: 1.5;
}

::ng-deep .mat-mdc-dialog-container {
  width: 860px !important;
  height: 630px !important;
  max-width: 860px !important;
  max-height: 630px !important;
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
}

::ng-deep .mat-horizontal-content-container {
  padding: 0px !important;
}

::ng-deep .mat-mdc-dialog-content {
  padding: 0px !important;
}

::ng-deep .mat-mdc-select-panel {
  margin-left: -10px;
}

.select-menu {
  background-color: white;
  height: 48px;
  border: 1px solid #ccc;
  border-radius: 8px;
}

/* Custom dropdown arrow for mat-select */
::ng-deep .dialog-content .mat-mdc-select-arrow-wrapper {
  transform: none;
}

::ng-deep .dialog-content .mat-mdc-select-arrow {
  display: none;
}

::ng-deep .dialog-content .mat-mdc-select-arrow-wrapper::after {
  content: "keyboard_arrow_down";
  font-family: "Material Icons";
  font-size: 20px;
  color: #666;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}

.range {
  width: 120px !important;
}

.form-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.flex-grow {
  flex-grow: 1;
}

.dialog-actions {
  position: absolute;
  bottom: 25px;
}

::ng-deep .mat-horizontal-stepper-header {
  display: none !important;
}

::ng-deep .mat-stepper-horizontal-line {
  display: none;
}
