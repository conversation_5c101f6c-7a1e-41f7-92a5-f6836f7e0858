<div class="dialog-header">
  <div class="flex justify-between items-center object-center pb-4">
    <h2 mat-dialog-title>New Training</h2>
    <button
      mat-icon-button
      (click)="onNoClick()"
      class="text-setSettinggray-400 hover:text-gray-600">
      <mat-icon>close</mat-icon>
    </button>
  </div>
  <!-- Training name display for step 2 and 3 -->
  @if (stepper.selectedIndex > 0) {
    <p class="training-name">{{ trainingForm.get('name')?.value }}</p>
  }
</div>

<!-- Horizontal divider line - only show for step 1 -->
@if (stepper.selectedIndex === 0) {
  <div class="dialog-divider"></div>
}

<div class="dialog-content">
  <mat-dialog-content>
    <mat-stepper #stepper>
      <mat-step>
        <form [formGroup]="trainingForm">
          <div class="flex flex-col">
            <label for="name" class="mb-2"
              >Name<span class="text-red-500">*</span></label
            >
            <input type="text" formControlName="name" class="form-input mb-6" />
            <label for="trainingData" class="mb-2"
              >Training Data<span class="text-red-500">*</span></label
            >
            <app-select-file-folder
              [preselectedFile]="preselectedFile"
              (selectedFile)="getSelectedFile($event)"
              class="mb-4"></app-select-file-folder>

            <label for="datasetVersion" class="mb-2">Dataset Version</label>
            <mat-select
              formControlName="datasetVersion"
              class="select-menu flex items-center pl-2 pr-2 mb-6">
              <mat-option value="Version 1">Version 1</mat-option>
            </mat-select>
            <label for="analysisGoal" class="mb-2"
              >Analysis Goal<span class="text-red-500">*</span></label
            >

            <div class="w-full flex flex-row gap-6">
              <mat-select
                formControlName="analysisGoalName"
                (selectionChange)="setSelectedValue($event)"
                class="select-menu flex items-center pl-2 pr-2 w-1/3">
                @for (task of mlTasks; track task) {
                  <mat-option [value]="task">{{ task }}</mat-option>
                }
              </mat-select>
              <mat-select
                formControlName="anaysisGoalType"
                class="select-menu flex items-center pl-2 pr-2 w-2/3">
                @for (model of mlModelNames; track model) {
                  <mat-option [value]="model.id">{{ model.name }}</mat-option>
                }
              </mat-select>
            </div>
          </div>
          <div class="flex justify-between items-center mt-10">
            <button mat-button class="bg-white" (click)="onNoClick()">
              Cancel
            </button>
            <span>Step {{ stepper.selectedIndex + 1 }} of 3</span>
            <button
              mat-flat-button
              matStepperNext
              [disabled]="checkStepper1FormError()">
              Next
            </button>
          </div>
        </form>
      </mat-step>
      <mat-step>
        <form
          [formGroup]="trainingForm"
          class="form-wrapper flex flex-col h-full">
          <div class="flex-grow">
            <label for="datasetVersion" class="mb-2"
              >Target<span class="text-red-500">*</span></label
            >
            <mat-select
              formControlName="selectedTarget"
              (selectionChange)="selectedTarget($event)"
              class="select-menu flex items-center pl-2 pr-2 mb-6">
              @for (target of targetColumnInfo; track target) {
                <mat-option [value]="target.id">{{ target.name }}</mat-option>
              }
            </mat-select>
            <label for="datasetVersion" class="mb-2"
              >Features<span class="text-red-500">*</span></label
            >

            <mat-select
              multiple
              formControlName="selectedFeatures"
              (selectionChange)="selectedFeatures($event)"
              class="select-menu flex items-center pl-2 pr-2 mb-6">
              @for (feature of featureColumnInfo; track feature) {
                <mat-option [value]="feature.id">
                  <mat-checkbox [checked]="feature.checked">
                    {{ feature.name }}
                  </mat-checkbox>
                </mat-option>
              }
            </mat-select>
            <label for="datasetVersion" class="mb-8"
              >Dataset Split<span class="text-red-500">*</span></label
            >
            <div class="w-full flex flex-row gap-7 items-center">
              <div class="flex items-center gap-2">
                <span>Train</span>
                <input
                  type="number"
                  formControlName="train_size"
                  min="5"
                  max="95"
                  class="select-menu range flex items-center pl-2 pr-2 w-full" />
                <span>%</span>
              </div>
              <!-- <mat-select readonly formControlName="train" class="select-menu flex items-center pl-2 pr-2 w-1/3">
                                <mat-option value="80%">80%</mat-option>
                            </mat-select> -->
              <div class="flex items-center gap-2">
                <span>Test</span>
                <input
                  type="number"
                  formControlName="test_size"
                  max="95"
                  min="5"
                  [(ngModel)]="test_size"
                  class="select-menu range flex items-center pl-2 pr-2 w-full" />
                <span>%</span>
              </div>
              <!-- <mat-select formControlName="test" class="select-menu flex items-center pl-2 pr-2 w-1/3">
                                <mat-option value="10%">10%</mat-option>
                            </mat-select> -->
              <!-- <span>Validation</span>
                            <mat-select formControlName="validation"
                                class="select-menu flex items-center pl-2 pr-2 w-1/3">
                                <mat-option value="10%">10%</mat-option>
                            </mat-select> -->
            </div>
            <div
              class="text-red-500 text-sm mt-1"
              *ngIf="
                trainingForm.get('train_size')?.errors ||
                trainingForm.get('test_size')?.errors
              ">
              Must be in range between 5 and 95
            </div>
          </div>
          <div class="flex justify-between items-center mt-40">
            <button mat-button matStepperPrevious>Back</button>
            <span>Step {{ stepper.selectedIndex + 1 }} of 3</span>
            <button
              mat-flat-button
              matStepperNext
              [disabled]="trainingForm.invalid">
              Next
            </button>
          </div>
        </form>
      </mat-step>
      <mat-step>
        <div class="form-wrapper flex flex-col">
          <div>
            <app-training-preview
              [trainingDetails]="getTrainingsDetails()"></app-training-preview>
          </div>

          <div class="flex justify-between items-center mt-20">
            <button mat-button matStepperPrevious>Back</button>
            <span>Step {{ stepper.selectedIndex + 1 }} of 3</span>
            <button
              mat-flat-button
              matStepperNext
              [disabled]="trainingForm.invalid"
              (click)="createTraining($event)">
              {{ stepper.selectedIndex === 2 ? 'Create Training' : 'Next' }}
            </button>
          </div>
        </div>
      </mat-step>
    </mat-stepper>
  </mat-dialog-content>
</div>
